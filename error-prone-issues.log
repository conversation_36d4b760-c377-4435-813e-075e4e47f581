/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13115​,13145​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13162​,13192​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13449​,13479​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13496​,13526​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13432​,13462​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13479​,13509​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13595​,13625​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13642​,13672​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13656​,13686​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13703​,13733​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:2733​,2752​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13656​,13686​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/IndexManager.java​:13703​,13733​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3061​,3123​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/AbstractOperationTemplate.java​:3545​,3611​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3070​,3132​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3554​,3620​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:2926​,2945​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13849​,13879​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13896​,13926​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3070​,3132​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3554​,3620​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:2926​,2945​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13849​,13879​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13896​,13926​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:1976​,1995​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-web/src/main/java/com/taobao/wireless/orange/web/aspect/ControllerLogAspect.java​:4146​,4163​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:2926​,2945​: warning: ​​[JavaTimeDefaultTimeZone] LocalDateTime.now() is not allowed because it silently uses the system default time-zone. You must pass an explicit time-zone (e.g., ZoneId.of("America/Los_Angeles")) to this method.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/JavaTimeDefaultTimeZone.md)
  Did you mean 'List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(ZoneId.systemDefault()), namespaceVersions, Arrays.asList(60, 120, 180));'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13849​,13879​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/product/generator/index/IndexManager.java​:13896​,13926​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:731​,741​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:813​,823​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-dal/src/main/java/com/taobao/wireless/orange/dal/handler/CustomMetaObjectHandler.java​:1070​,1080​: warning: ​​[JavaUtilDate] Date has a bad API that leads to bugs; prefer java.time.Instant or LocalDate.
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/info/JavaUtilDate.md)​
/Users/<USER>/workspace/orange/orange-be/orange-external/src/main/java/com/taobao/wireless/orange/external/diamond/DiamondDataCallbackDemo.java​:2143​,2163​: warning: ​​[CatchAndPrintStackTrace] Logging or rethrowing exceptions should usually be preferred to catching and calling printStackTrace
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/CatchAndPrintStackTrace.md)​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3070​,3132​: warning: ​​[MissingOverride] validateStatus implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateStatus(OperationContext context);'?​
/Users/<USER>/workspace/orange/orange-be/orange-manager/src/main/java/com/taobao/wireless/orange/manager/release/workflow/AbstractOperationTemplate.java​:3554​,3620​: warning: ​​[MissingOverride] validateParameters implements method in OperationStrategy; expected @Override
    (see https://code.alibaba-inc.com/AlibabaCodeGuideline/JavaCodeGuideline/blob/master/bugpattern/major/MissingOverride.md)
  Did you mean '@Override public abstract void validateParameters(OperationContext context);'?​
