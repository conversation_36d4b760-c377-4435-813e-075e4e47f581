syntax = "proto3";

package com.taobao.wireless.orange.common.model;

import "common.proto";

option java_package = "com.taobao.wireless.orange.common.model.proto";
option java_outer_classname = "GrayConfigOuterClass";
option java_multiple_files = true;

message GrayConfigProto {
  // 协议版本，用于指导解析配置文件
  string schema_version = 1;

  // 命名空间名称
  string namespace = 2;

  // 灰度配置仅可能为全量配置：FULL
  ConfigStrategyProto strategy = 3;

  ConfigTypeProto type = 4;

  // 包含发布单的具体变更
  repeated OrderProto orders = 5;

  // 发布单参数相关的条件对象
  repeated ConditionProto conditions = 6;
}

message OrderProto {
  // 发布单版本号，唯一标识一个发布单
  int64 version = 1;

  // 删除的参数
  repeated string offline_parameters = 2;

  // 发布单修改和新增的参数对象
  repeated ParameterProto parameters = 3;
}


