package com.taobao.wireless.orange.common.filter;

import com.alibaba.fastjson.JSON;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.annotation.Order;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.InvocationHandler;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.invocation.filter.ClientFilter;
import com.taobao.hsf.plugins.eagleeye.EagleEyeConstants;
import com.taobao.hsf.util.concurrent.ListenableFuture;
import com.taobao.wireless.orange.common.constant.enums.LogType;
import com.taobao.wireless.orange.common.util.LogParamUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * HSF Client 统一日志切面
 * 记录HSF客户端调用的详细信息，包括请求参数、响应结果、执行时间等
 *
 * <AUTHOR>
 */
@Slf4j(topic = "api")
@Order(8)
public class LogHSFClientFilter implements ClientFilter {

    @Override
    public ListenableFuture<RPCResult> invoke(InvocationHandler nextHandler, Invocation invocation) throws Throwable {
        String eTraceId = EagleEye.getTraceId();
        if (eTraceId != null) {
            invocation.put(EagleEyeConstants.EAGLEEYE_TRACE_ID_KEY, EagleEye.getTraceId());
        }

        invocation.put(EagleEyeConstants.EAGLEEYE_RPC_ID_KEY,
                EagleEye.getRpcId() == null ? "null" : EagleEye.getRpcId());

        try {
            return nextHandler.invoke(invocation);
        } catch (Exception e) {
            log.error(JSON.toJSONString(LogParamUtil.generateLog(LogType.HSF_CLIENT, invocation, null, e)));
            throw e;
        }
    }

    @Override
    public void onResponse(Invocation invocation, RPCResult rpcResult) {
        log.info(JSON.toJSONString(LogParamUtil.generateLog(LogType.HSF_CLIENT, invocation, null, rpcResult)));
    }
}
