<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>
    <groupId>com.taobao.wireless</groupId>
    <artifactId>orange</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>orange</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <autoconfig-maven-plugin.version>1.2-fixcompress-SNAPSHOT</autoconfig-maven-plugin.version>
        <jacoco-plugin.version>0.8.11</jacoco-plugin.version>
        <java.version>21</java.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <maven-compile-plugin.version>3.11.0</maven-compile-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <mybatis-starter.version>3.0.4</mybatis-starter.version>
        <pandora-boot-maven-plugin.version>10.0.1.3</pandora-boot-maven-plugin.version>
        <pandora-boot.version>2024-12-release-fix2</pandora-boot.version>
        <spring-boot.version>3.2.5</spring-boot.version>
        <schedulerx2.version>1.12.1</schedulerx2.version>
        <normandy-credential-spring-boot-starter.version>1.2.0</normandy-credential-spring-boot-starter.version>
        <aliyun-sdk-oss.version>3.18.2</aliyun-sdk-oss.version>
        <protobuf.version>3.25.3</protobuf.version>
        <protobuf-maven-plugin.version>0.6.1</protobuf-maven-plugin.version>
        <fastjson.version>1.2.65</fastjson.version>
    </properties>

    <modules>
        <module>orange-web</module>
        <module>orange-service</module>
        <module>orange-manager</module>
        <module>orange-external</module>
        <module>orange-dal</module>
        <module>orange-common</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-external</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-manager</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>orange-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jcl</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.26</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>99.0-does-not-exist</version>
            </dependency>
            <dependency>
                <groupId>servlet-api</groupId>
                <artifactId>servlet-api</artifactId>
                <version>999-no-exist-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.diamond</groupId>
                <artifactId>diamond-client</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>1.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.5.9</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>3.5.9</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>buc-spring-boot-starter</artifactId>
                <version>1.8.7-jakarta</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.mtl</groupId>
                <artifactId>mtl-open-sdk-consumer</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao</groupId>
                <artifactId>wmcc-client</artifactId>
                <version>2.4.0</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.49</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>1.7.4</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>hsf-sdk</artifactId>
                <version>2.2.9.1--2020-04-release</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>1.7.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.6.12</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>${schedulerx2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.ihr</groupId>
                <artifactId>amdplatform-service-api</artifactId>
                <version>2.0.80</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>3.17.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>buc.sso.client.plugin-sdk</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <!-- Normandy 资源凭证 SDK -->
            <dependency>
                <groupId>com.alibaba.normandy.credential</groupId>
                <artifactId>normandy-credential-spring-boot-starter</artifactId>
                <version>${normandy-credential-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.alibaba.maven.plugins</groupId>
                    <artifactId>error-prone-maven-plugin</artifactId>
                    <version>1.5.2-rc12</version>
                    <configuration>
                        <ruleProfileName>Error-Prone</ruleProfileName>
                        <engineVersion>2.23.0-SNAPSHOT</engineVersion>
                    </configuration>
                    <executions>
                        <execution>
                            <id>check</id>
                            <goals>
                                <goal>check</goal>
                                <goal>testCheck</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-plugin.version}</version>
                    <configuration>
                        <propertyName>argLine</propertyName>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                                <goal>report-aggregate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compile-plugin.version}</version>
                    <configuration>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.alibaba.citrus.tool</groupId>
                    <artifactId>autoconfig-maven-plugin</artifactId>
                    <version>${autoconfig-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.alibaba.maven.plugins</groupId>
                <artifactId>error-prone-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
